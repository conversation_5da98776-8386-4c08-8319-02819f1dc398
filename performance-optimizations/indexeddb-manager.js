/**
 * IndexedDB Manager for Chrome Extension
 * Handles persistent storage with automatic cleanup and size limits
 */

class IndexedDBManager {
  constructor() {
    this.dbName = 'SnapDashboardDB';
    this.dbVersion = 1;
    this.db = null;
    this.maxStorageSize = 100 * 1024 * 1024; // 100MB limit
    this.maxRecords = 10000; // Limit records per store
    this.dataRetentionDays = 30; // Keep data for 30 days
    
    // Store configurations
    this.stores = {
      salesData: { keyPath: 'id', autoIncrement: true },
      listingsData: { keyPath: 'asin' },
      analyticsData: { keyPath: 'timestamp' },
      adSpendData: { keyPath: 'date' },
      chartCache: { keyPath: 'cacheKey' },
      userSettings: { keyPath: 'setting' }
    };
  }

  /**
   * Initialize IndexedDB connection
   */
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => {
        console.error('❌ IndexedDB failed to open:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB connected successfully');
        
        // Setup error handling
        this.db.onerror = (event) => {
          console.error('❌ IndexedDB error:', event.target.error);
        };
        
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object stores
        Object.entries(this.stores).forEach(([storeName, config]) => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, config);
            
            // Add indexes for common queries
            if (storeName === 'salesData') {
              store.createIndex('timestamp', 'timestamp', { unique: false });
              store.createIndex('marketplace', 'marketplace', { unique: false });
            }
            if (storeName === 'listingsData') {
              store.createIndex('lastUpdated', 'lastUpdated', { unique: false });
            }
            if (storeName === 'chartCache') {
              store.createIndex('expiry', 'expiry', { unique: false });
            }
            
            console.log(`📦 Created object store: ${storeName}`);
          }
        });
      };
    });
  }

  /**
   * Store real-time Amazon data with automatic cleanup
   */
  async storeAmazonData(type, data) {
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([type], 'readwrite');
    const store = transaction.objectStore(type);
    
    try {
      // Add timestamp and expiry
      const enrichedData = {
        ...data,
        timestamp: Date.now(),
        expiry: Date.now() + (this.dataRetentionDays * 24 * 60 * 60 * 1000)
      };
      
      await store.put(enrichedData);
      
      // Trigger cleanup if needed
      await this.cleanupExpiredData(type);
      await this.enforceStorageLimit(type);
      
      console.log(`💾 Stored ${type} data:`, data.id || data.asin || 'new record');
      
    } catch (error) {
      console.error(`❌ Failed to store ${type} data:`, error);
      throw error;
    }
  }

  /**
   * Retrieve data with caching
   */
  async getData(type, key = null) {
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([type], 'readonly');
    const store = transaction.objectStore(type);
    
    try {
      let result;
      
      if (key) {
        result = await store.get(key);
      } else {
        result = await store.getAll();
      }
      
      return result;
      
    } catch (error) {
      console.error(`❌ Failed to get ${type} data:`, error);
      throw error;
    }
  }

  /**
   * Clean up expired data automatically
   */
  async cleanupExpiredData(storeName) {
    const transaction = this.db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const now = Date.now();
    
    try {
      const allRecords = await store.getAll();
      let deletedCount = 0;
      
      for (const record of allRecords) {
        if (record.expiry && record.expiry < now) {
          await store.delete(record.id || record.asin || record.timestamp);
          deletedCount++;
        }
      }
      
      if (deletedCount > 0) {
        console.log(`🗑️ Cleaned up ${deletedCount} expired records from ${storeName}`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to cleanup ${storeName}:`, error);
    }
  }

  /**
   * Enforce storage size limits
   */
  async enforceStorageLimit(storeName) {
    const transaction = this.db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    
    try {
      const count = await store.count();
      
      if (count > this.maxRecords) {
        // Get oldest records and delete them
        const oldestRecords = await store.index('timestamp').getAll(null, count - this.maxRecords);
        
        for (const record of oldestRecords) {
          await store.delete(record.id || record.asin || record.timestamp);
        }
        
        console.log(`🗑️ Removed ${oldestRecords.length} old records from ${storeName}`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to enforce storage limit for ${storeName}:`, error);
    }
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    if (!this.db) return null;
    
    const stats = {};
    
    for (const storeName of Object.keys(this.stores)) {
      try {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const count = await store.count();
        
        stats[storeName] = {
          recordCount: count,
          maxRecords: this.maxRecords,
          usage: `${count}/${this.maxRecords}`
        };
        
      } catch (error) {
        stats[storeName] = { error: error.message };
      }
    }
    
    return stats;
  }

  /**
   * Clear all data (for debugging/reset)
   */
  async clearAllData() {
    if (!this.db) return;
    
    for (const storeName of Object.keys(this.stores)) {
      try {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        await store.clear();
        console.log(`🗑️ Cleared all data from ${storeName}`);
      } catch (error) {
        console.error(`❌ Failed to clear ${storeName}:`, error);
      }
    }
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('🔒 IndexedDB connection closed');
    }
  }
}

// Global instance for Chrome extension
window.IndexedDBManager = new IndexedDBManager();

// Auto-initialize when script loads
window.IndexedDBManager.init().catch(error => {
  console.error('❌ Failed to initialize IndexedDB:', error);
});

// Cleanup on extension unload
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onSuspend.addListener(() => {
    window.IndexedDBManager.close();
  });
}

// Export for global use
window.storeAmazonData = (type, data) => window.IndexedDBManager.storeAmazonData(type, data);
window.getStoredData = (type, key) => window.IndexedDBManager.getData(type, key);
window.getStorageStats = () => window.IndexedDBManager.getStorageStats();
